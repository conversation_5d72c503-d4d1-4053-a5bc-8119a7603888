<template>
  <div class="w-[350px] p-5 flex flex-col bg-[#f5f7f9]">
    <h3 class="w-full h-full h-7 text-5 text-center leading-[28px] title">思维导图创作中心</h3>
    <!--下面表单部分-->
    <div class="flex-grow overflow-y-auto">
      <div class="mt-[30ppx]">
        <el-text tag="b">您的需求？</el-text>
        <el-input
          v-model="formData.prompt"
          maxlength="1024"
          :rows="5"
          class="w-100% mt-15px"
          input-style="border-radius: 7px;"
          placeholder="请输入提示词，让AI帮你完善"
          show-word-limit
          type="textarea"
        />
        <div class="flex gap-2 mt-[15px]">
          <el-button
            class="flex-1"
            type="primary"
            :loading="isGenerating"
            @click="emits('submit', formData)"
          >
            智能生成思维导图
          </el-button>
          <el-button
            type="primary"
            :loading="chooseFileLoading"
            @click="handleChooseFile"
            :disabled="isGenerating"
          >
            <Icon icon="fa:cloud-upload" />
          </el-button>
        </div>
        <!-- 选中文件显示区域 -->
        <div v-if="selectedFiles.length > 0" class="mt-3">
          <el-text tag="b" class="text-sm">已选择文件：</el-text>
          <div class="mt-2 space-y-1">
            <div
              v-for="(file, index) in selectedFiles"
              :key="index"
              class="flex items-center justify-between bg-white p-2 rounded text-sm"
            >
              <span class="truncate flex-1">{{ file.fileName || file.name }}</span>
              <el-button type="text" size="small" @click="removeFile(index)" class="ml-2">
                <Icon icon="ep:close" />
              </el-button>
            </div>
          </div>
          <el-button type="text" size="small" @click="clearAllFiles" class="mt-2">
            清空所有文件
          </el-button>
        </div>
      </div>
      <div class="mt-[30px]">
        <el-text tag="b">使用已有内容生成？</el-text>
        <el-input
          v-model="generatedContent"
          maxlength="1024"
          :rows="5"
          class="w-100% mt-15px"
          input-style="border-radius: 7px;"
          placeholder="例如：童话里的小屋应该是什么样子？"
          show-word-limit
          type="textarea"
        />
        <el-button
          class="!w-full mt-[15px]"
          type="primary"
          @click="emits('directGenerate', generatedContent)"
          :disabled="isGenerating"
        >
          直接生成
        </el-button>
      </div>
    </div>
    <!-- 文件选择组件 -->
    <ChooseFile
      ref="chooseFileRef"
      @success="getChooseList"
      :temporaryKbId="temporaryKbId"
      :token="token"
      localDocName="mindmap_documentChooseList"
      localKnowName="mindmap_knowledgeChooseList"
    />
  </div>
</template>

<script setup lang="ts">
import { MindMapContentExample } from '@/views/ai/utils/constants'
import ChooseFile from '@/views/knowledge/dataTraining/components/ChooseFile/index.vue'
import { useUserStore } from '@/store/modules/user'
import axios from 'axios'

const emits = defineEmits(['submit', 'directGenerate'])
defineProps<{
  isGenerating: boolean
}>()

const userStore = useUserStore()
const apikeys = userStore.getAppConfig
const message = useMessage()

// 提交的提示词字段
const formData = reactive({
  prompt: ''
})

const generatedContent = ref(MindMapContentExample) // 已有的内容

// 文件选择相关状态
const chooseFileRef = ref()
const chooseFileLoading = ref(false)
const selectedFiles = ref<any[]>([])
const fileData = ref<any[]>([])

// 临时知识库配置 - 这里需要根据实际情况配置
const temporaryKbId = ref(apikeys.kl_cloud_data || '')
const token = ref(apikeys.ragflow_token || '')

// 文件选择相关方法
const handleChooseFile = () => {
  chooseFileRef.value.open()
}

// 处理文件选择回调
const getChooseList = async (fileObj: any) => {
  const { fileList } = fileObj

  chooseFileLoading.value = true
  try {
    const promiseList = fileList.map(async (item) => {
      const content = await getDocumentContent(
        item.documentId,
        item.datasetId || item.dataset_id,
        item.Authorization,
        0
      )
      return {
        ...item,
        content: content
      }
    })

    const results = await Promise.all(promiseList)
    fileData.value = results
    selectedFiles.value = fileList

    // 将文件内容整合到prompt中
    if (results.length > 0) {
      const fileContents = results
        .map((item) => `文件：${item.fileName || item.name}\n内容：${item.content}`)
        .join('\n\n')
      formData.prompt = formData.prompt
        ? `${formData.prompt}\n\n基于以下文件内容：\n${fileContents}`
        : `基于以下文件内容：\n${fileContents}`
    }

    message.success(`成功选择 ${fileList.length} 个文件`)
  } catch (error) {
    console.error('获取文件内容失败:', error)
    message.error('获取文件内容失败')
  } finally {
    chooseFileLoading.value = false
  }
}

// 获取文档内容
const getDocumentContent = async (
  documentId: string,
  key: string,
  Authorization: string,
  pageSize = 0
) => {
  try {
    const response = await axios({
      url: `${apikeys.ragflow_url}/api/v1/datasets/${key}/documents/${documentId}/chunks`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: Authorization,
        Accept: '*/*'
      },
      params: { offset: 1, limit: pageSize || 1000 }
    })

    const { data } = response
    if (data?.data?.chunks) {
      return data.data.chunks
        .map((chunk: any) => chunk.content_with_weight || chunk.content)
        .join('\n')
    }
    return ''
  } catch (error) {
    console.error('获取文档内容失败:', error)
    return ''
  }
}

// 移除单个文件
const removeFile = (index: number) => {
  selectedFiles.value.splice(index, 1)
  fileData.value.splice(index, 1)

  // 更新localStorage中的选择状态
  updateLocalStorage()

  // 重新生成prompt
  if (fileData.value.length > 0) {
    const fileContents = fileData.value
      .map((item) => `文件：${item.fileName || item.name}\n内容：${item.content}`)
      .join('\n\n')
    formData.prompt = `基于以下文件内容：\n${fileContents}`
  } else {
    // 如果没有文件了，清空prompt中的文件内容部分
    formData.prompt = formData.prompt.replace(/基于以下文件内容：[\s\S]*$/, '').trim()
  }
}

// 清空所有文件
const clearAllFiles = () => {
  selectedFiles.value = []
  fileData.value = []

  // 清空localStorage中的选择状态
  localStorage.removeItem('mindmap_documentChooseList')
  localStorage.removeItem('mindmap_knowledgeChooseList')

  // 清空prompt中的文件内容部分
  formData.prompt = formData.prompt.replace(/基于以下文件内容：[\s\S]*$/, '').trim()
}

// 更新localStorage中的选择状态
const updateLocalStorage = () => {
  // 分离本地文档和知识库文件
  const documentList = selectedFiles.value.filter((file) => file.documentId)
  const knowledgeList = selectedFiles.value.filter((file) => !file.documentId)

  // 更新localStorage
  localStorage.setItem('mindmap_documentChooseList', JSON.stringify(documentList))
  localStorage.setItem('mindmap_knowledgeChooseList', JSON.stringify(knowledgeList))
}

defineExpose({
  setGeneratedContent(newContent: string) {
    // 设置已有的内容，在生成结束的时候将结果赋值给该值
    generatedContent.value = newContent
  }
})
</script>

<style lang="scss" scoped>
.title {
  color: var(--el-color-primary);
}
</style>
